import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt

def fetch_net_premiums(ticker):
    # Download option chain expirations
    stock = yf.Ticker(ticker)
    exps = stock.options
    data = []
    # Loop through expirations
    for exp in exps:
        opt = stock.option_chain(exp)
        calls = opt.calls.copy()
        puts = opt.puts.copy()
        # Compute total premium = strike * volume for calls and puts
        calls['premium_value'] = calls['lastPrice'] * calls['volume']
        puts['premium_value'] = puts['lastPrice'] * puts['volume']
        total_calls = calls['premium_value'].sum()
        total_puts = puts['premium_value'].sum()
        data.append({'expiration': exp, 'net_premium': total_calls - total_puts})
    df = pd.DataFrame(data)
    df['expiration'] = pd.to_datetime(df['expiration'])
    df.sort_values('expiration', inplace=True)
    return df

if __name__ == '__main__':
    ticker = 'AAPL'
    df = fetch_net_premiums(ticker)

    # Plot
    plt.figure(figsize=(10, 6))
    plt.plot(df['expiration'], df['net_premium'] / 1e6, marker='o', linestyle='-')
    plt.title(f'Net Option Premiums for {ticker}')
    plt.xlabel('Expiration Date')
    plt.ylabel('Net Premium (Millions USD)')
    plt.grid(True)
    plt.tight_layout()
    plt.show()